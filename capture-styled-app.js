const puppeteer = require('puppeteer');

async function captureAfterStyling() {
  const browser = await puppeteer.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });
  
  try {
    console.log('=== CAPTURING INFRANODUS-STYLED APPLICATION ===');
    
    // Navigate to podcast view
    await page.goto('http://localhost:5174/podcast/1', { waitUntil: 'networkidle0' });
    await page.waitForTimeout(3000);
    
    // Take screenshot
    await page.screenshot({ path: 'infranodus-styled-app.png', fullPage: true });
    console.log('✓ InfraNodus-styled screenshot saved');
    
    // Test menu toggle
    await page.click('#menuLink');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'infranodus-styled-menu-open.png', fullPage: true });
    console.log('✓ Menu open screenshot saved');
    
  } catch (error) {
    console.error('Error during capture:', error);
  }
  
  await browser.close();
}

captureAfterStyling().catch(console.error);
